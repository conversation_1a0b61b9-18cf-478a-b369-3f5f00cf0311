import { Component, OnInit, ViewChild } from '@angular/core';
import { StorageService } from 'src/app/core/services/storage.service';
import * as moment from 'moment';
import { Hospital } from 'src/app/shared/models/hospital.model';
import { Direction } from '@angular/cdk/bidi';
import { TranslateService } from '@ngx-translate/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import {
  BaseComponentCanDeactivateDirective
} from '../../../shared/components/base-candeactive/base-candeactivate.directive';

@Component({
  selector: 'app-config',
  templateUrl: './config.component.html',
  styleUrls: ['./config.component.scss'],
})
export class ConfigComponent extends BaseComponentCanDeactivateDirective implements OnInit {

  @ViewChild('hospitalComponent') hospitalComponent: any;
  @ViewChild('timeoffsComponent') timeoffsComponent: any;
  @ViewChild('staffComponent') staffComponent: any;
  @ViewChild('otherConfigsComponent') otherConfigsComponent: any;

  public hospital: Hospital;
  public dir: Direction = 'ltr';

  unsaved = false;
  currentTabIndex = 0;
  isSaving = false;
  showSaveButton = true;
  hasUnsavedChanges = false;
  isFormValid = true;

  constructor(
    private storageService: StorageService,
    private translate: TranslateService
  ) {

    super();
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    const user = this.storageService.getUser();
    if (user && user.profile && user.profile.hospital) {
      this.hospital = user.profile.hospital;
    }
  }

  hasUnsavedData(): boolean {
    return this.unsaved;
  }

  handleSaveChange(saved: boolean) {
    this.unsaved = !saved;
    this.hasUnsavedChanges = !saved;
    this.updateFormValidity();
    console.log('Save status changed:', { saved, hasUnsavedChanges: this.hasUnsavedChanges, isFormValid: this.isFormValid });
  }

  handleValidityChange(isValid: boolean) {
    this.isFormValid = isValid;
    console.log('Form validity changed:', { isValid, hasUnsavedChanges: this.hasUnsavedChanges });
  }

  updateFormValidity() {
    // Check form validity based on current tab
    switch (this.currentTabIndex) {
      case 0: // Cabinet/Hospital tab
        this.isFormValid = this.hospitalComponent?.isValidForm() ?? true;
        break;
      default:
        this.isFormValid = true; // Other tabs don't have form validation requirements
        break;
    }
  }

  onTabChange(event: MatTabChangeEvent) {
    this.currentTabIndex = event.index;
    // Update save button visibility based on tab
    this.updateSaveButtonVisibility();
    this.updateFormValidity();
  }

  updateSaveButtonVisibility() {
    // Show save button for all tabs
    this.showSaveButton = true;
  }

  saveCurrentTab() {
    this.isSaving = true;

    switch (this.currentTabIndex) {
      case 0: // Cabinet/Hospital tab
        if (this.hospitalComponent && this.hospitalComponent.formSubmit) {
          this.hospitalComponent.formSubmit();
        }
        break;
      case 1: // Breaks tab
        if (this.timeoffsComponent && this.timeoffsComponent.save) {
          this.timeoffsComponent.save();
        } else {
          // Timeoffs saves automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
      case 2: // Team tab
        if (this.staffComponent && this.staffComponent.save) {
          this.staffComponent.save();
        } else {
          // Staff saves automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
      case 3: // Others tab
        if (this.otherConfigsComponent && this.otherConfigsComponent.save) {
          this.otherConfigsComponent.save();
        } else {
          // Other configs save automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
    }

    setTimeout(() => {
      this.isSaving = false;
    }, 1000);
  }
}
