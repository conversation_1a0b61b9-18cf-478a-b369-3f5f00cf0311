<div class="config-container">
  <mat-tab-group mat-align-tabs="center" (selectedTabChange)="onTabChange($event)">
    <mat-tab [label]="'config.office' | translate">
      <app-hospital
        #hospitalComponent
        (changeStatus)="handleSaveChange($event)"
        (validityChange)="handleValidityChange($event)"
        [hospital]="hospital">
      </app-hospital>
    </mat-tab>
    <mat-tab [label]="'config.breaks' | translate">
      <app-fix-timeoffs
        #timeoffsComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-fix-timeoffs>
    </mat-tab>
    <mat-tab [label]="'config.team' | translate">
      <app-staff
        #staffComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-staff>
    </mat-tab>
    <mat-tab [label]="'config.others' | translate">
      <app-other-configurations
        #otherConfigsComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-other-configurations>
    </mat-tab>
  </mat-tab-group>

  <!-- Global Save Button -->
  <div class="save-button-container" *ngIf="showSaveButton">
    <app-custom-button
      color="primary"
      [loading]="isSaving"
      [disabled]="!hasUnsavedChanges || !isFormValid"
      (click)="saveCurrentTab()">
      {{ 'config.generalInfo.save' | translate }}
    </app-custom-button>
    <!-- Debug info (remove in production) -->
    <div class="debug-info" style="font-size: 10px; color: #666; margin-top: 5px;">
      Changes: {{hasUnsavedChanges}} | Valid: {{isFormValid}} | Tab: {{currentTabIndex}}
    </div>
  </div>
</div>
